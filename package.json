{"name": "vue-manage-system", "version": "5.5.0", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "serve": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "*", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.6.3", "countup.js": "^2.8.0", "echarts": "^5.5.0", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.6.3", "md-editor-v3": "^2.11.2", "nprogress": "^0.2.0", "pinia": "^2.1.7", "vue": "^3.4.5", "vue-cropper": "1.1.1", "vue-echarts": "^6.6.9", "vue-router": "^4.2.5", "vue-schart": "^2.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^3.0.0", "@vue/compiler-sfc": "^3.1.2", "typescript": "^4.6.4", "unplugin-auto-import": "^0.11.2", "unplugin-vue-components": "^0.22.4", "vite": "^3.0.0", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-tsc": "^0.38.4"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}